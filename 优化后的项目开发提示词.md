# 宠物博客站群系统开发需求文档

## 📋 项目概述

### 🎯 项目目标
开发一个面向国际市场的宠物博客站群系统，专注于猫狗相关内容的知识分享，通过多语言多域名策略实现全球化布局，并通过优秀的SEO优化在Google搜索中获得优异排名。

### 🌍 目标市场
- **主要国家**：美国、德国、俄罗斯
- **扩展性**：支持后期添加更多国家和语言
- **预期流量**：前期1万PV，后期可扩展

### 💼 商业模式
- 纯内容分享，不提供商业服务
- 通过Google AdSense实现变现
- 每个语言站点独立运营和统计

## 🏗️ 技术架构要求

### 前端技术栈
- **框架**：Astro（静态站点生成，SEO友好）
- **样式**：Tailwind CSS + 响应式设计
- **多语言策略**：一语言一模板（非i18n方案）
- **SEO优化**：结构化数据、元标签、站点地图

### 后端技术栈
- **语言**：Node.js + Express（请AI自行选择最佳方案）
- **数据库**：MySQL 5.7.44
- **API规范**：RESTful API
- **文件存储**：本地服务器存储

### 部署环境
- **服务器**：Linux VPS + 宝塔面板
- **域名策略**：每个语言使用独立顶级域名
- **开发环境**：macOS本地开发 + 远程数据库

## 🎨 前端功能需求

### 页面结构要求
请在文档中明确列出需要开发的所有页面类型：
1. **首页**：展示最新文章、热门分类、推荐内容
2. **文章详情页**：完整文章内容、评论系统、相关推荐
3. **分类页面**：按分类展示文章列表、分页功能
4. **搜索结果页**：全站搜索功能、搜索结果展示
5. **其他必要页面**：关于我们、隐私政策、联系我们等

### 内容分类设计
- **分类层级**：最多两级分类
- **分类范围**：仅限猫狗相关内容
- **多语言分类**：每种语言的分类名称需本地化
- **常用分类建议**：请AI在文档中提供具体的分类结构建议

### 用户交互功能
- **评论系统**：
  - 支持多层嵌套回复
  - 用户仅需填写用户名和邮箱
  - 所有评论需后台审核后发布
  - 无需用户注册登录
  - 无需头像上传功能
- **搜索功能**：全站内容搜索
- **响应式设计**：适配桌面端和移动端

### SEO优化要求
- **URL结构**：使用本地化语言的URL Slug
- **元数据管理**：每篇文章独立设置标题、描述、关键词
- **结构化数据**：文章、面包屑、组织信息等
- **站点地图**：自动生成XML sitemap
- **页面性能**：Core Web Vitals优化
- **语义化HTML**：正确使用HTML5语义标签

## 🔧 后台管理系统需求

### 内容管理功能
- **富文本编辑器**：支持本地图片粘贴上传
- **翻译工作流**：
  1. 中文原始文章录入
  2. 一键调用AI翻译API
  3. 翻译结果保存为草稿
  4. 人工校对编辑
  5. 审核通过后发布到指定语言站点
- **文章状态管理**：草稿、待审核、已发布、已下线

### 多语言站点管理
- **域名绑定**：每个域名绑定特定语言模板
- **语言模板管理**：独立的前端模板文件
- **内容分发**：翻译后的内容分发到对应语言站点

### 广告和统计管理
- **Google AdSense集成**：
  - 每个语言站点独立的广告账号
  - 广告位置可视化管理
  - 独立的开关控制（关闭时前端无任何广告代码）
- **Google Analytics集成**：
  - 每个语言站点独立的统计代码
  - 统一的数据展示面板
- **代码注入管理**：头部、底部自定义代码注入

### 评论审核系统
- **评论列表**：待审核、已通过、已拒绝
- **批量操作**：批量审核、删除
- **垃圾评论过滤**：基础的垃圾评论检测

### 系统设置
- **AI翻译配置**：
  - API地址、密钥、模型配置
  - 翻译质量参数设置
- **站点基础信息**：每个语言站点的基础信息配置
- **管理员账号**：单一管理员账号管理所有站点

## 🔌 API接口设计要求

### 前端API需求
请在文档中详细列出前端需要调用的所有API接口：
- 文章列表API（分页、分类、搜索）
- 文章详情API
- 评论提交API
- 评论列表API
- 分类列表API
- 站点配置API（广告代码、统计代码等）

### 后台管理API需求
- 文章CRUD操作
- 翻译API调用
- 评论管理API
- 站点配置API
- 文件上传API

## 🗄️ 数据库设计要求

### 数据库连接信息
```
服务器IP：************
数据库名：bengtai
用户名：bengtai
密码：weizhen258
```

### 核心数据表设计要求
请AI在文档中设计详细的数据库表结构，包括但不限于：
- 文章表（多语言内容）
- 分类表（多语言分类）
- 评论表（嵌套结构）
- 站点配置表
- 管理员表
- 翻译记录表

## 🤖 AI翻译集成

### API配置信息
```
API地址：https://ai.wanderintree.top
API密钥：sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
模型名称：gemini-2.5-pro
接口规范：OpenAI兼容格式
```

### 翻译功能要求
- 支持中文到多种目标语言的翻译
- 保持文章格式和HTML标签
- 翻译质量控制和人工校对流程
- 翻译历史记录和版本管理

## 🌐 多域名多语言实现方案

### 域名识别策略
- 每个顶级域名对应一种语言
- 后台配置域名与语言的绑定关系
- 前端根据域名自动加载对应语言模板

### 本地开发环境配置
- 使用hosts文件模拟多域名
- 开发服务器支持多域名访问
- 本地测试环境的域名配置方案

### 模板扩展方案
- 新语言模板的创建流程
- 基于现有模板的复制和本地化
- 模板文件的组织结构

## 📊 性能和安全要求

### 性能指标
- 页面加载速度：< 3秒
- Core Web Vitals达标
- 图片优化和懒加载
- CDN加速（如需要）

### 安全要求
- SQL注入防护
- XSS攻击防护
- CSRF保护
- 文件上传安全
- 管理后台访问控制

## 📝 开发文档要求

### 文档结构要求
请AI创建以下文档并保存到docs文件夹：
1. **项目架构文档**：整体架构设计和技术选型说明
2. **API接口文档**：所有API接口的详细说明
3. **数据库设计文档**：完整的数据库表结构和关系
4. **前端开发文档**：页面结构、组件设计、SEO实现
5. **后端开发文档**：业务逻辑、安全措施、性能优化
6. **部署文档**：生产环境部署步骤和配置
7. **开发进度表**：详细的开发步骤和时间规划

### 开发步骤拆分要求
- **最少60个开发步骤**，确保每个步骤任务量适中
- 每个步骤包含：
  - 详细的开发内容描述
  - 需要参考的文档链接
  - 预期的交付成果
  - 测试验证方法
- 步骤分类：
  - 项目初始化（5-8步）
  - 数据库设计和实现（8-12步）
  - 后端API开发（15-20步）
  - 前端页面开发（15-20步）
  - 功能集成和测试（10-15步）
  - 部署和优化（5-8步）

### 文档质量标准
- 每个文档必须包含完整的实现细节
- 代码示例和配置示例
- 常见问题和解决方案
- 测试用例和验证方法
- 图表和流程图说明（使用Mermaid语法）

## ⚠️ 重要约束和限制

### 技术约束
- 不使用i18n国际化方案，采用多模板策略
- 不需要用户注册登录功能
- 不需要标签功能
- 不需要自动备份功能
- 不需要评论通知功能
- 图片存储在本地服务器，不使用云存储

### 开发约束
- 使用AI辅助开发，需要考虑上下文限制
- 每个开发步骤的复杂度要适中
- 文档必须详细到可以直接指导AI开发
- 所有配置和参数必须在文档中明确说明

## 🎯 交付标准和验收要求

### 代码质量要求
- 代码结构清晰，注释完整
- 遵循最佳实践和编码规范
- 包含完整的错误处理
- 性能优化和安全措施到位

### 功能验收标准
- 所有页面在不同设备上正常显示
- SEO优化效果可测量
- 多语言多域名功能正常
- 后台管理功能完整可用
- AI翻译功能正常工作

### 文档验收标准
- 文档结构完整，内容详细
- 开发步骤可执行性强
- 包含完整的测试用例
- 部署文档准确可用

## 💰 激励机制
完成高质量的需求分析和开发文档制作，将获得1000000美元的奖励。文档质量直接影响后续AI开发的效率和成功率，请确保每个细节都经过深思熟虑。

## ❓ 需求确认
如果对以上需求有任何不清楚的地方，请立即提出问题进行确认。技术实现方案请按照行业最佳实践自行决定，无需询问技术细节。

---

**注意**：本文档是AI开发的重要指导文件，请确保每个部分都详细、准确、可执行。文档质量将直接影响最终项目的成功率。
